'use client';

import React, { useState } from 'react';
import Image from 'next/image';

/**
 * 全局悬浮客服按钮组件
 * 固定在右下角，悬停时显示客服二维码
 */
export default function FloatingCustomerService() {
  const [showQrCode, setShowQrCode] = useState(false);

  return (
    <div className="fixed bottom-6 right-6 z-[9999]">
      {/* 客服按钮 */}
      <div
        className="relative"
        onMouseEnter={() => setShowQrCode(true)}
        onMouseLeave={() => setShowQrCode(false)}
      >
        <button
          className="w-14 h-14 bg-[#824dfc] hover:bg-[#7041e6] rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group"
          title="联系客服"
        >
          {/* 客服图标 */}
          <svg
            className="w-7 h-7 text-white group-hover:scale-110 transition-transform duration-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
            />
          </svg>
        </button>

        {/* 客服二维码悬浮窗 */}
        {showQrCode && (
          <div
            className="absolute bottom-full right-0 mb-3 bg-white rounded-lg shadow-xl p-4 border border-gray-200 w-48"
            onMouseEnter={() => setShowQrCode(true)}
            onMouseLeave={() => setShowQrCode(false)}
          >
            {/* 小箭头 */}
            <div className="absolute top-full right-4 w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-l-transparent border-r-transparent border-t-white"></div>
            
            <div className="text-center">
              <Image
                src="/home/<USER>"
                alt="客服二维码"
                width={140}
                height={140}
                className="rounded mx-auto"
              />
              <div className="text-sm text-gray-600 mt-3 font-medium">
                微信扫码联系客服
              </div>
              <div className="text-xs text-gray-500 mt-1">
                工作时间：9:00-18:00
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 脉冲动画效果 */}
      <div className="absolute inset-0 w-14 h-14 bg-[#824dfc] rounded-full animate-ping opacity-20"></div>
    </div>
  );
}
